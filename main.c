#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>

#define PHYSICAL_BLOCK_SIZE 1024
#define LOGICAL_BLOCK_SIZE 1024
#define NAME_LEN 14
#define START_PARTITION_TABLE 0x1be

//分区表结构
struct par_table_entry {
	char boot_indicator;	//导入指示器，绝大多数情况都是0
	char start_chs_val[3];	//起始柱面磁头扇区值，3个字节分别对应柱面号、磁头号、扇区号
	char par_type;			//分区类型
	char end_chs_val[3];	//终止柱面磁头扇区值
	int start_sector;		//起始盘块号
	int par_size;			//分区大小
};

// 超级块结构体
struct super_block
{
  unsigned short s_ninodes;		// 节点数。
  unsigned short s_nzones;		// 逻辑块数。
  unsigned short s_imap_blocks;	// i 节点位图所占用的数据块数。
  unsigned short s_zmap_blocks;	// 逻辑块位图所占用的数据块数。
  unsigned short s_firstdatazone;	// 第一个数据逻辑块号。
  unsigned short s_log_zone_size;	// log(数据块数/逻辑块)。（以2 为底）。
  unsigned long s_max_size;		// 文件最大长度。
  unsigned short s_magic;		// 文件系统魔数。
};

// i节点结构体
struct d_inode
{
  unsigned short i_mode;		// 文件类型和属性(rwx 位)。
  unsigned short i_uid;			// 用户id（文件拥有者标识符）。
  unsigned long i_size;			// 文件大小（字节数）。
  unsigned long i_time;			// 修改时间（自1970.1.1:0 算起，秒）。
  unsigned char i_gid;			// 组id(文件拥有者所在的组)。
  unsigned char i_nlinks;		// 链接数（多少个文件目录项指向该i 节点）。
  unsigned short i_zone[9];		// 直接(0-6)、间接(7)或双重间接(8)逻辑块号。
								// zone 是区的意思，可译成区段，或逻辑块。
};

//目录项结构
struct dir_entry{
	unsigned short inode;	//i节点号
	char name[NAME_LEN];	//文件名
};

struct super_block sblock;		//超级块
struct par_table_entry pte[4];	//分区表数组
FILE* fd;						//文件指针
char physical_block[PHYSICAL_BLOCK_SIZE]; //存储物理块
char logical_block[LOGICAL_BLOCK_SIZE];  //存储逻辑块
char *inode_bitmap;		//i节点位图指针
char *zone_bitmap;
//char *logical_bitmap;	//逻辑块位图指针,本实例未使用

//读取一个物理块
void get_physical_block(int block_num)
{
	//减1是因为物理盘块是从1开始计数
	fseek(fd, (block_num - 1) * PHYSICAL_BLOCK_SIZE, SEEK_SET);
	fread(physical_block, PHYSICAL_BLOCK_SIZE, 1, fd);
}

//读取第一个分区的一个逻辑块
void get_partition_logical_block(int block_num)
{
	//block_num前面加的1表示在第一个分区前还有一个主引导记录（MBR）块，
	//后面加的1是因为物理盘块是从1开始计数的,而逻辑块是从0开始计数的
	get_physical_block(1 + block_num + 1);
	memcpy(logical_block, physical_block, LOGICAL_BLOCK_SIZE);
}

//读取分区表
void get_partition_table()
{
	int i = 0;

	//分区表有4个16字节的表项组成，第一个表项的起始地址为START_PARTITION_TABLE
	get_physical_block( 1 );	//分区表在物理盘块的第1块
	memcpy(pte, &physical_block[START_PARTITION_TABLE], sizeof(pte));
	for(i = 0; i < 4; i++)
	{
		printf("**************pattition table%d****************\n", i+1);
		printf("Boot Indicator:%d\n", pte[i].boot_indicator);
		printf("start CHS value:0x%04x\n", pte[i].start_chs_val);
		printf("partition type:%ld\n", pte[i].par_type);
		printf("end CHS value:0x%04x\n", pte[i].end_chs_val);
		printf("start sector:%d\n", pte[i].start_sector);
		printf("partition size:%d\n", pte[i].par_size);
	}
}

//读取第一个分区的超级块
void get_super_block()
{	
	get_partition_logical_block( 1 );
	memcpy(&sblock, logical_block, sizeof(sblock));
	
	printf("**************super block****************\n");
	printf("ninodes：%d\n", sblock.s_ninodes);
	printf("nzones：%d\n", sblock.s_nzones);
	printf("imap_blocks：%d\n", sblock.s_imap_blocks);
	printf("zmap_blocks：%d\n", sblock.s_zmap_blocks);
	printf("firstdatazone：0x%04x\n", sblock.s_firstdatazone);
	printf("log_zone_size：%d\n", sblock.s_log_zone_size);
	printf("max_size：0x%x = %dByte\n", sblock.s_max_size,sblock.s_max_size);
	printf("magic：0x%x\n", sblock.s_magic);
}	


//加载i节点位图
void load_inode_bitmap()
{
	inode_bitmap = (char*)malloc(sblock.s_imap_blocks * LOGICAL_BLOCK_SIZE);
	int i = 0;
	for(i = 0; i < sblock.s_imap_blocks; i++)
	{
		//i节点位图前有1个引导块和一个超级块
		get_partition_logical_block(1 + 1 + i);	
		memcpy(&inode_bitmap[i * LOGICAL_BLOCK_SIZE], &logical_block, LOGICAL_BLOCK_SIZE);
	}
}

//根据i节点位图判断其对应的i节点是否有效
//参数inode_id为i节点的id
//有效返回1，无效返回0
int is_inode_valid(unsigned short inode_id)
{
	if(inode_id > sblock.s_ninodes)
		return 0;
		
	char byte = inode_bitmap[(inode_id - 1) / 8]; //inode_id减1是因为i节点是从1开始计数的
	return (byte >> (7 - (inode_id - 1) % 8) ) & 0x1;	//取一个字节中的某位与1做位运算
}

//根据i节点id读取i节点
void get_inode(unsigned short inode_id, struct d_inode* inode)
{
	//一个引导块，一个超级块，sblock.s_imap_blocks个i节点位图，sblock.s_zmap_blocks个逻辑块位图	
	//一个i节点占32个字节，一个盘块有LOGICAL_BLOCK_SIZE/32个节点，所以inode_id/(LOGICAL_BLOCK_SIZE/32)
	//减1是因为i节点号是从1开始计数的，而逻辑块号是从0开始计数的
	//inode_blocknum是i节点在逻辑块中的偏移块数
	int inode_blocknum = 1 + 1 + sblock.s_imap_blocks + sblock.s_zmap_blocks + (inode_id - 1) / (LOGICAL_BLOCK_SIZE/32) ;
	get_partition_logical_block(inode_blocknum);
	memcpy((char*)inode, &logical_block[((inode_id - 1) % sizeof(struct d_inode)) * sizeof(struct d_inode)], sizeof(struct d_inode));
}

//递归打印i节点下的目录
void print_inode(unsigned short id, int tab_count, const char* name)
{
	int i, m, n;
	struct d_inode inode;
	struct dir_entry dir;
	
	//如果i节点号对应在i节点位图相应位的值为1,说明此i节点已使用
	//否则说明此i节点无用或已被删除，则直接返回
	if(is_inode_valid(id) != 1)
		return;
		
	get_inode(id, &inode);
	tab_count++;
	unsigned short mode = inode.i_mode >> 12; //高4位存放的是文件类型
	
	//如果是目录文件
	if(mode == 4)
	{
		//打印tab键，为了使目录有层次感
		for(i=0; i<tab_count; i++)
		{
			printf("\t");
		}
		printf("%s\n", name);
		
		//循环读取i节点中的i_zones[]数组
		for(m = 0; m<7; m++)
		{
			//如果数组数据为0，则跳过
			if(inode.i_zone[m] == 0)
				continue;
			
			//一个逻辑块最多存储64个目录项,循环读取64个目录项
			//其中前两项分别为 . 和 .. 
			for(n = 0; n < 64; n++)
			{
				get_partition_logical_block(inode.i_zone[m]);
				//将逻辑块中的数据拷贝到目录项结构体
				memcpy((char*)&dir, &logical_block[n * sizeof(dir)], sizeof(dir));
				
				//如果是 .和..则继续循环
				if(n == 0 || n == 1)
					continue;
					
				//如果目录项中没有内容了则不再读取
				if(dir.inode == 0)
					break;
				
				//递归打印子目录
				print_inode(dir.inode, tab_count, dir.name);
			}
		}
	}
	
	//如果是常规文件
	else if(mode == 8)
	{
		for(i=0; i<tab_count; i++)
		{
			printf("\t");
		}
		printf("%s\n", name);
	}
	//如果块设备文件、字符设备文件等其他类型文件，请读者尝试自己实现
}

void load_zone_bitmap()
{
	zone_bitmap = (char*)malloc(sblock.s_zmap_blocks * LOGICAL_BLOCK_SIZE);
	int i;
	for(i = 0; i < sblock.s_zmap_blocks; i++)
	{
		int logical_block_num = 2 + sblock.s_imap_blocks + i; // 1 boot block + 1 superblock + inode bitmap blocks
		get_partition_logical_block(logical_block_num);
		memcpy(&zone_bitmap[i * LOGICAL_BLOCK_SIZE], logical_block, LOGICAL_BLOCK_SIZE);
	}
}
int count_set_bits(unsigned char byte)
{
	int count = 0;
	while (byte) {
		count += byte & 1;
		byte >>= 1;
	}
	return count;
}
void print_df()
{
	int allocated_blocks = 0;
	for (int i = 0; i < sblock.s_zmap_blocks * LOGICAL_BLOCK_SIZE; i++) {
		allocated_blocks += count_set_bits((unsigned char)zone_bitmap[i]);
	}
	int free_blocks = sblock.s_nzones - allocated_blocks;
	int use_percent = (sblock.s_nzones > 0) ? (allocated_blocks * 100) / sblock.s_nzones : 0;
	printf("Filesystem    1K-blocks      Used Available Use%% Mounted on\n");
	printf("minix         %9d %9d %9d %3d%% /\n", sblock.s_nzones, allocated_blocks, free_blocks, use_percent);
}
unsigned short find_in_directory(unsigned short dir_inode, const char* name)
{
	struct d_inode dir;
	get_inode(dir_inode, &dir);
	if ((dir.i_mode >> 12) != 4) {
		printf("Error: not a directory\n");
		return 0;
	}
	for (int m = 0; m < 7; m++) {
		if (dir.i_zone[m] == 0) continue;
		get_partition_logical_block(dir.i_zone[m]);
		int entries_per_block = LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry);
		for (int n = 0; n < entries_per_block; n++) {
			struct dir_entry entry;
			memcpy(&entry, &logical_block[n * sizeof(struct dir_entry)], sizeof(struct dir_entry));
			if (entry.inode == 0) break;
			if (strncmp(entry.name, name, NAME_LEN) == 0) {
				return entry.inode;
			}
		}
	}
	return 0; // not found
}
unsigned short find_inode_by_path(const char* path)
{
	char* path_copy = strdup(path);
	if (path_copy == NULL) {
		printf("Memory allocation failed\n");
		return 0;
	}
	unsigned short current_inode = 1; // root
	char* token = strtok(path_copy, "/");
	while (token != NULL) {
		if (strlen(token) > 0) {
			current_inode = find_in_directory(current_inode, token);
			if (current_inode == 0) {
				free(path_copy);
				return 0;
			}
		}
		token = strtok(NULL, "/");
	}
	free(path_copy);
	return current_inode;
}
void print_file_contents(unsigned short file_inode)
{
	struct d_inode file;
	get_inode(file_inode, &file);
	if ((file.i_mode >> 12) != 8) {
		printf("Error: not a regular file\n");
		return;
	}
	unsigned long bytes_to_read = file.i_size;
	unsigned long bytes_read = 0;

	// Direct blocks
	for (int i = 0; i < 7 && bytes_read < bytes_to_read; i++) {
		if (file.i_zone[i] == 0) continue;
		get_partition_logical_block(file.i_zone[i]);
		unsigned long to_read = (bytes_read + LOGICAL_BLOCK_SIZE > bytes_to_read) ? bytes_to_read - bytes_read : LOGICAL_BLOCK_SIZE;
		fwrite(logical_block, 1, to_read, stdout);
		bytes_read += to_read;
	}

	// Indirect blocks
	if (bytes_read < bytes_to_read && file.i_zone[7] != 0) {
		get_partition_logical_block(file.i_zone[7]);
		unsigned short* indirect_ptrs = (unsigned short*)logical_block;
		int num_ptrs = LOGICAL_BLOCK_SIZE / sizeof(unsigned short);
		for (int i = 0; i < num_ptrs && bytes_read < bytes_to_read; i++) {
			if (indirect_ptrs[i] == 0) continue;
			get_partition_logical_block(indirect_ptrs[i]);
			unsigned long to_read = (bytes_read + LOGICAL_BLOCK_SIZE > bytes_to_read) ? bytes_to_read - bytes_read : LOGICAL_BLOCK_SIZE;
			fwrite(logical_block, 1, to_read, stdout);
			bytes_read += to_read;
		}
	}

	// Double indirect blocks
	if (bytes_read < bytes_to_read && file.i_zone[8] != 0) {
		get_partition_logical_block(file.i_zone[8]);
		unsigned short* double_indirect_ptrs = (unsigned short*)logical_block;
		int num_double_ptrs = LOGICAL_BLOCK_SIZE / sizeof(unsigned short);
		for (int j = 0; j < num_double_ptrs && bytes_read < bytes_to_read; j++) {
			if (double_indirect_ptrs[j] == 0) continue;
			get_partition_logical_block(double_indirect_ptrs[j]);
			unsigned short* indirect_ptrs = (unsigned short*)logical_block;
			for (int k = 0; k < num_double_ptrs && bytes_read < bytes_to_read; k++) {
				if (indirect_ptrs[k] == 0) continue;
				get_partition_logical_block(indirect_ptrs[k]);
				unsigned long to_read = (bytes_read + LOGICAL_BLOCK_SIZE > bytes_to_read) ? bytes_to_read - bytes_read : LOGICAL_BLOCK_SIZE;
				fwrite(logical_block, 1, to_read, stdout);
				bytes_read += to_read;
			}
		}
	}
}
// 将逻辑块写入磁盘
void put_partition_logical_block(int block_num) {
    fseek(fd, (2 + block_num - 1) * PHYSICAL_BLOCK_SIZE, SEEK_SET);
    fwrite(logical_block, PHYSICAL_BLOCK_SIZE, 1, fd);
}

// 更新 i 节点位图到磁盘
void write_inode_bitmap() {
    for (int i = 0; i < sblock.s_imap_blocks; i++) {
        memcpy(logical_block, &inode_bitmap[i * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
        put_partition_logical_block(2 + i);
    }
}

// 更新数据块位图到磁盘
void write_zone_bitmap() {
    for (int i = 0; i < sblock.s_zmap_blocks; i++) {
        memcpy(logical_block, &zone_bitmap[i * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
        put_partition_logical_block(2 + sblock.s_imap_blocks + i);
    }
}

// 释放 i 节点
void deallocate_inode(unsigned short inode_id) {
    if (inode_id == 0 || inode_id > sblock.s_ninodes) return;
    int byte_index = (inode_id - 1) / 8;
    int bit = (inode_id - 1) % 8;
    inode_bitmap[byte_index] &= ~(1 << bit);
}

// 释放数据块
void deallocate_zone(unsigned short zone_id) {
    if (zone_id >= sblock.s_nzones) return;
    int byte_index = zone_id / 8;
    int bit = zone_id % 8;
    zone_bitmap[byte_index] &= ~(1 << bit);
}

// 将 i 节点写回磁盘
void put_inode(unsigned short inode_id, struct d_inode* inode) {
    int inodes_per_block = LOGICAL_BLOCK_SIZE / sizeof(struct d_inode);
    int block_offset = (inode_id - 1) / inodes_per_block;
    int inode_blocknum = 2 + sblock.s_imap_blocks + sblock.s_zmap_blocks + block_offset;
    get_partition_logical_block(inode_blocknum);
    int offset = ((inode_id - 1) % inodes_per_block) * sizeof(struct d_inode);
    memcpy(&logical_block[offset], inode, sizeof(struct d_inode));
    put_partition_logical_block(inode_blocknum);
}

// 从目录中删除指定条目
int remove_entry_from_directory(unsigned short dir_inode, const char* name) {
    struct d_inode dir;
    get_inode(dir_inode, &dir);
    if ((dir.i_mode >> 12) != 4) { // 检查是否为目录
        printf("Error: not a directory\n");
        return -1;
    }
    for (int m = 0; m < 7; m++) {
        if (dir.i_zone[m] == 0) continue;
        get_partition_logical_block(dir.i_zone[m]);
        int entries_per_block = LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry);
        for (int n = 0; n < entries_per_block; n++) {
            struct dir_entry entry;
            memcpy(&entry, &logical_block[n * sizeof(struct dir_entry)], sizeof(struct dir_entry));
            if (entry.inode == 0) continue;
            if (strncmp(entry.name, name, NAME_LEN) == 0) {
                entry.inode = 0; // 将条目的 i 节点号置为 0，表示删除
                memcpy(&logical_block[n * sizeof(struct dir_entry)], &entry, sizeof(struct dir_entry));
                put_partition_logical_block(dir.i_zone[m]);
                return 0;
            }
        }
    }
    return -1;
}

// 释放 i 节点的所有数据块
void deallocate_zones(struct d_inode* inode) {
    for (int i = 0; i < 7; i++) { // 直接块
        if (inode->i_zone[i] != 0) {
            deallocate_zone(inode->i_zone[i]);
            inode->i_zone[i] = 0;
        }
    }
    if (inode->i_zone[7] != 0) { // 间接块
        get_partition_logical_block(inode->i_zone[7]);
        unsigned short* ptrs = (unsigned short*)logical_block;
        int num_ptrs = LOGICAL_BLOCK_SIZE / sizeof(unsigned short);
        for (int i = 0; i < num_ptrs; i++) {
            if (ptrs[i] != 0) deallocate_zone(ptrs[i]);
        }
        deallocate_zone(inode->i_zone[7]);
        inode->i_zone[7] = 0;
    }
}
unsigned short allocate_inode() {
    for (int i = 0; i < sblock.s_ninodes; i++) {
        int byte_index = i / 8;
        int bit = i % 8;
        if ((inode_bitmap[byte_index] & (1 << bit)) == 0) {
            inode_bitmap[byte_index] |= (1 << bit);
            return i + 1;
        }
    }
    return 0;
}

// 分配数据块
unsigned short allocate_zone() {
    for (int i = 0; i < sblock.s_nzones; i++) {
        int byte_index = i / 8;
        int bit = i % 8;
        if ((zone_bitmap[byte_index] & (1 << bit)) == 0) {
            zone_bitmap[byte_index] |= (1 << bit);
            return i;
        }
    }
    return 0;
}

// 在目录中添加条目
int add_entry_to_directory(unsigned short dir_inode, const char* name, unsigned short inode_id) {
    struct d_inode dir;
    get_inode(dir_inode, &dir);
    if ((dir.i_mode >> 12) != 4) {
        printf("Error: not a directory\n");
        return -1;
    }
    for (int m = 0; m < 7; m++) {
        if (dir.i_zone[m] == 0) {
            dir.i_zone[m] = allocate_zone();
            dir.i_size += LOGICAL_BLOCK_SIZE;
            put_inode(dir_inode, &dir);
        }
        get_partition_logical_block(dir.i_zone[m]);
        int entries_per_block = LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry);
        for (int n = 0; n < entries_per_block; n++) {
            struct dir_entry entry;
            memcpy(&entry, &logical_block[n * sizeof(struct dir_entry)], sizeof(struct dir_entry));
            if (entry.inode == 0) {
                entry.inode = inode_id;
                strncpy(entry.name, name, NAME_LEN);
                memcpy(&logical_block[n * sizeof(struct dir_entry)], &entry, sizeof(struct dir_entry));
                put_partition_logical_block(dir.i_zone[m]);
                return 0;
            }
        }
    }
    return -1;
}
int is_directory_empty(unsigned short inode_id) {
    struct d_inode dir;
    get_inode(inode_id, &dir);
    if ((dir.i_mode >> 12) != 4) return -1;
    get_partition_logical_block(dir.i_zone[0]);
    int entries_per_block = LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry);
    int count = 0;
    for (int i = 0; i < entries_per_block; i++) {
        struct dir_entry entry;
        memcpy(&entry, &logical_block[i * sizeof(struct dir_entry)], sizeof(struct dir_entry));
        if (entry.inode != 0) count++;
    }
    return count <= 2; // 只有 "." 和 ".." 时返回 1
}

int main(int argc, char* argv[])
{
	int bit;
	struct d_inode* inode = (struct d_inode*)malloc(sizeof(struct d_inode));
	unsigned short file_inode = find_inode_by_path("/usr/root/hello.c");
    unsigned short parent_inode = find_inode_by_path("/usr/root");
    unsigned short dir_inode = find_in_directory(parent_inode, "test");
    unsigned short new_inode = allocate_inode();
	char* path = "D:\\minix\\harddisk.img";
	printf("尝试打开文件: %s\n", path);
	fd = fopen(path, "rb+");
	if (fd == NULL) {
		printf("打开文件失败: %s\n", path);
		perror("错误详情");
		return 1;
	}
        
    //读取分区表
	get_partition_table();
		//读取超级块
    get_super_block();


    //加载i节点逻辑块位图
	load_inode_bitmap();
    load_zone_bitmap();
    print_df();
	if (file_inode != 0) {
		printf("Contents of /usr/root/hello.c:\n");
		print_file_contents(file_inode);
	} else {
		printf("File /usr/root/hello.c not found\n");
	}
    	//打印/usr/src/linux-0.11.org/memory.c的内容
	file_inode = find_inode_by_path("/usr/src/linux-0.11.org/memory.c");
	if (file_inode != 0) {
		printf("Contents of /usr/src/linux-0.11.org/memory.c:\n");
		print_file_contents(file_inode);
	} else {
		printf("File /usr/src/linux-0.11.org/memory.c not found\n");
	}
    file_inode = find_in_directory(parent_inode, "hello.c"); // 假设已有此函数
    if (file_inode != 0) {
        remove_entry_from_directory(parent_inode, "hello.c");
        struct d_inode file;
        get_inode(file_inode, &file);
        deallocate_zones(&file);
        memset(&file, 0, sizeof(struct d_inode));
        put_inode(file_inode, &file);
        deallocate_inode(file_inode);
        write_inode_bitmap();
        write_zone_bitmap();
        printf("已删除 /usr/root/hello.c\n");
    } else {
        printf("文件 /usr/root/hello.c 未找到\n");
    }
    
    parent_inode = find_inode_by_path("/usr/root");
    if (new_inode != 0) {
        struct d_inode new_dir;
        memset(&new_dir, 0, sizeof(struct d_inode));
        new_dir.i_mode = (4 << 12) | 0755; // 目录类型，权限 0755
        new_dir.i_nlinks = 2; // "." 和 ".."
        new_dir.i_size = LOGICAL_BLOCK_SIZE;
        new_dir.i_zone[0] = allocate_zone();
        put_inode(new_inode, &new_dir);

        // 初始化目录内容："." 和 ".."
        get_partition_logical_block(new_dir.i_zone[0]);
        struct dir_entry dot = {new_inode, "."};
        struct dir_entry dotdot = {parent_inode, ".."};
        memcpy(&logical_block[0], &dot, sizeof(struct dir_entry));
        memcpy(&logical_block[sizeof(struct dir_entry)], &dotdot, sizeof(struct dir_entry));
        put_partition_logical_block(new_dir.i_zone[0]);

        // 在父目录中添加 "test"
        add_entry_to_directory(parent_inode, "test", new_inode);
        write_inode_bitmap();
        write_zone_bitmap();
        printf("已创建目录 /usr/root/test\n");
    } else {
        printf("无法创建目录：无可用 i 节点\n");
    }
    parent_inode = find_inode_by_path("/usr/root/test");
    new_inode = allocate_inode();
    if (new_inode != 0) {
        struct d_inode new_file;
        memset(&new_file, 0, sizeof(struct d_inode));
        new_file.i_mode = (8 << 12) | 0644; // 普通文件类型，权限 0644
        new_file.i_nlinks = 1;
        new_file.i_size = 0;
        put_inode(new_inode, &new_file);

        add_entry_to_directory(parent_inode, "hi.c", new_inode);
        write_inode_bitmap();
        printf("已创建文件 /usr/root/test/hi.c\n");
    } else {
        printf("无法创建文件：无可用 i 节点\n");
    }
    parent_inode = find_inode_by_path("/usr/root");

    if (dir_inode != 0) {
        if (is_directory_empty(dir_inode)) {
            remove_entry_from_directory(parent_inode, "test");
            struct d_inode dir;
            get_inode(dir_inode, &dir);
            deallocate_zones(&dir);
            memset(&dir, 0, sizeof(struct d_inode));
            put_inode(dir_inode, &dir);
            deallocate_inode(dir_inode);
            write_inode_bitmap();
            write_zone_bitmap();
            printf("已删除目录 /usr/root/test\n");
        } else {
            printf("目录 /usr/root/test 不为空，无法删除\n");
        }
    } else {
        printf("目录 /usr/root/test 未找到\n");
    }
	//i节点位图的第一位对应文件系统的根节点
	//如果第一位为1，则打印根节点
	bit = is_inode_valid(1);
	if(bit == 1)
		print_inode(1, -1, "\\");
	else
		printf("root node lost！\n");

	return 0;
}
